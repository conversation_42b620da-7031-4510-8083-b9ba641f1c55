import {useCallback, useEffect, useState} from 'react';
import {DatabaseConnection, DatabaseTable, QueryResult} from '../types';

export const useDatabase = () => {
    const [connections, setConnections] = useState<DatabaseConnection[]>([]);
    const [activeConnection, setActiveConnection] = useState<DatabaseConnection | null>(null);
    const [tables, setTables] = useState<DatabaseTable[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Load connections from localStorage on mount
    useEffect(() => {
        const savedConnections = localStorage.getItem('db_connections');
        if (savedConnections) {
            try {
                const parsed = JSON.parse(savedConnections);
                setConnections(parsed);
            } catch (err) {
                console.error('Failed to load saved connections:', err);
            }
        }
    }, []);

    // Save connections to localStorage whenever connections change
    useEffect(() => {
        localStorage.setItem('db_connections', JSON.stringify(connections));
    }, [connections]);

    const createConnection = useCallback((connectionData: Omit<DatabaseConnection, 'id' | 'isConnected' | 'lastConnected'>) => {
        const newConnection: DatabaseConnection = {
            ...connectionData,
            id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            isConnected: false,
        };

        setConnections(prev => [...prev, newConnection]);
        return newConnection;
    }, []);

    const updateConnection = useCallback((id: string, updates: Partial<DatabaseConnection>) => {
        setConnections(prev =>
            prev.map(conn =>
                conn.id === id ? {...conn, ...updates} : conn
            )
        );
    }, []);

    const deleteConnection = useCallback((id: string) => {
        setConnections(prev => prev.filter(conn => conn.id !== id));
        if (activeConnection?.id === id) {
            setActiveConnection(null);
            setTables([]);
        }
    }, [activeConnection]);

    const connectToDatabase = useCallback(async (connection: DatabaseConnection) => {
        setIsLoading(true);
        setError(null);

        try {
            // Simulate database connection
            await new Promise(resolve => setTimeout(resolve, 1000));

            // In a real app, this would make an actual database connection
            const updatedConnection = {
                ...connection,
                isConnected: true,
                lastConnected: new Date(),
            };

            updateConnection(connection.id, updatedConnection);
            setActiveConnection(updatedConnection);

            // Mock table data
            const mockTables: DatabaseTable[] = [
                {
                    name: 'users',
                    rowCount: 150,
                    columns: [
                        {name: 'id', type: 'INTEGER', nullable: false, primaryKey: true},
                        {name: 'username', type: 'VARCHAR(50)', nullable: false, primaryKey: false},
                        {name: 'email', type: 'VARCHAR(100)', nullable: false, primaryKey: false},
                        {
                            name: 'created_at',
                            type: 'TIMESTAMP',
                            nullable: false,
                            primaryKey: false,
                            defaultValue: 'CURRENT_TIMESTAMP'
                        },
                    ]
                },
                {
                    name: 'posts',
                    rowCount: 450,
                    columns: [
                        {name: 'id', type: 'INTEGER', nullable: false, primaryKey: true},
                        {name: 'user_id', type: 'INTEGER', nullable: false, primaryKey: false},
                        {name: 'title', type: 'VARCHAR(200)', nullable: false, primaryKey: false},
                        {name: 'content', type: 'TEXT', nullable: true, primaryKey: false},
                        {
                            name: 'created_at',
                            type: 'TIMESTAMP',
                            nullable: false,
                            primaryKey: false,
                            defaultValue: 'CURRENT_TIMESTAMP'
                        },
                    ]
                },
                {
                    name: 'categories',
                    rowCount: 25,
                    columns: [
                        {name: 'id', type: 'INTEGER', nullable: false, primaryKey: true},
                        {name: 'name', type: 'VARCHAR(100)', nullable: false, primaryKey: false},
                        {name: 'description', type: 'TEXT', nullable: true, primaryKey: false},
                    ]
                }
            ];

            setTables(mockTables);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to connect to database';
            setError(errorMessage);
        } finally {
            setIsLoading(false);
        }
    }, [updateConnection]);

    const disconnectFromDatabase = useCallback(() => {
        if (activeConnection) {
            updateConnection(activeConnection.id, {isConnected: false});
            setActiveConnection(null);
            setTables([]);
        }
    }, [activeConnection, updateConnection]);

    const executeQuery = useCallback(async (sql: string): Promise<QueryResult> => {
        if (!activeConnection) {
            throw new Error('No active database connection');
        }

        setIsLoading(true);
        const startTime = Date.now();

        try {
            // Simulate query execution
            await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

            // Mock query results based on SQL type
            const upperSQL = sql.toUpperCase().trim();
            let mockResult: QueryResult;

            if (upperSQL.startsWith('SELECT')) {
                mockResult = {
                    columns: ['id', 'name', 'email', 'created_at'],
                    rows: [
                        {id: 1, name: 'John Doe', email: '<EMAIL>', created_at: '2023-01-15 10:30:00'},
                        {id: 2, name: 'Jane Smith', email: '<EMAIL>', created_at: '2023-01-16 14:20:00'},
                        {id: 3, name: 'Bob Johnson', email: '<EMAIL>', created_at: '2023-01-17 09:15:00'},
                    ],
                    rowCount: 3,
                    executionTime: Date.now() - startTime,
                };
            } else if (upperSQL.startsWith('INSERT')) {
                mockResult = {
                    columns: [],
                    rows: [],
                    rowCount: 1,
                    executionTime: Date.now() - startTime,
                };
            } else if (upperSQL.startsWith('UPDATE')) {
                mockResult = {
                    columns: [],
                    rows: [],
                    rowCount: Math.floor(Math.random() * 5) + 1,
                    executionTime: Date.now() - startTime,
                };
            } else if (upperSQL.startsWith('DELETE')) {
                mockResult = {
                    columns: [],
                    rows: [],
                    rowCount: Math.floor(Math.random() * 3) + 1,
                    executionTime: Date.now() - startTime,
                };
            } else {
                mockResult = {
                    columns: [],
                    rows: [],
                    rowCount: 0,
                    executionTime: Date.now() - startTime,
                };
            }

            return mockResult;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Query execution failed';
            return {
                columns: [],
                rows: [],
                rowCount: 0,
                executionTime: Date.now() - startTime,
                error: errorMessage,
            };
        } finally {
            setIsLoading(false);
        }
    }, [activeConnection]);

    return {
        connections,
        activeConnection,
        tables,
        isLoading,
        error,
        createConnection,
        updateConnection,
        deleteConnection,
        connectToDatabase,
        disconnectFromDatabase,
        executeQuery,
    };
};
