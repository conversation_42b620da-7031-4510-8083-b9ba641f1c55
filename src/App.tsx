import {useState} from 'react';
import {ToolTabs} from './components/ToolTabs';
import {JSONFormatter} from './components/JSONFormatter';
import {SQLPrefixerComponent} from './components/SQLPrefixer';
import {PropertiesYamlConverterComponent} from './components/PropertiesYamlConverter';
import {ToolTab} from './types';
import {Settings} from 'lucide-react';

function App() {
    const [activeTab, setActiveTab] = useState<ToolTab>('sql-prefixer');

    const renderActiveTab = () => {
        switch (activeTab) {
            case 'json-formatter':
                return <JSONFormatter/>;
            case 'sql-prefixer':
                return <SQLPrefixerComponent/>;
            case 'properties-yaml-converter':
                return <PropertiesYamlConverterComponent/>;
            default:
                return <JSONFormatter/>;
        }
    };

    return (
        <div className="h-screen flex flex-col bg-gray-50">
            {/* Header */}
            <header className="bg-white border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <Settings className="w-5 h-5 text-white"/>
                        </div>
                        <div>
                            <h1 className="text-xl font-semibold text-gray-900">Dev Tools App</h1>
                            <p className="text-sm text-gray-500">Professional development utilities</p>
                        </div>
                    </div>

                    <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-500">
                            v1.0.0
                        </div>
                    </div>
                </div>
            </header>

            {/* Navigation Tabs */}
            <ToolTabs activeTab={activeTab} onTabChange={setActiveTab}/>

            {/* Main Content */}
            <main className="flex-1 overflow-hidden">
                {renderActiveTab()}
            </main>
        </div>
    );
}

export default App;
