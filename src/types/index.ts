// Database related types
export interface DatabaseConnection {
    id: string;
    name: string;
    type: 'sqlite' | 'mysql' | 'postgresql' | 'mongodb';
    host?: string;
    port?: number;
    database: string;
    username?: string;
    password?: string;
    isConnected: boolean;
    lastConnected?: Date;
}

export interface DatabaseTable {
    name: string;
    columns: DatabaseColumn[];
    rowCount: number;
}

export interface DatabaseColumn {
    name: string;
    type: string;
    nullable: boolean;
    primaryKey: boolean;
    defaultValue?: string;
}

export interface QueryResult {
    columns: string[];
    rows: Record<string, any>[];
    rowCount: number;
    executionTime: number;
    error?: string;
}

// SQL Processor types
export interface SQLQuery {
    id: string;
    name: string;
    sql: string;
    description?: string;
    createdAt: Date;
    updatedAt: Date;
    tags: string[];
}

export interface SQLTemplate {
    id: string;
    name: string;
    description: string;
    template: string;
    category: string;
    parameters: SQLTemplateParameter[];
}

export interface SQLTemplateParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'date';
    required: boolean;
    defaultValue?: string;
    description?: string;
}

// Tool types
export type ToolTab = 'database' | 'sql-processor' | 'api-tester' | 'json-formatter' | 'sql-prefixer' | 'properties-yaml-converter';

export interface Tool {
    id: ToolTab;
    name: string;
    description: string;
    icon: string;
}

// Common types
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}

export interface PaginationParams {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

export interface SearchParams {
    query: string;
    filters?: Record<string, any>;
}

// SQL前缀工具相关类型
export interface SQLPrefixResult {
  original: string;
  modified: string;
  tablesFound: string[];
  sqlType?: string; // SQL语句类型
  isModifying?: boolean; // 是否为修改类型的语句
}

export interface BatchSQLResult {
  results: SQLPrefixResult[];
  summary: {
    total: number;
    processed: number;
    tablesModified: number; // 只统计修改类型语句涉及的表数量
    queryStatements: number; // 查询语句数量
    modifyStatements: number; // 修改语句数量
  };
}

export interface MultipleDatabaseResult {
  [databaseName: string]: {
    results: SQLPrefixResult[];
    summary: {
      total: number;
      processed: number;
      tablesModified: number; // 只统计修改类型语句涉及的表数量
      queryStatements: number; // 查询语句数量
      modifyStatements: number; // 修改语句数量
    };
  };
}

// Properties和YAML转换工具相关类型
export interface ConversionResult {
  success: boolean;
  result?: string;
  error?: string;
}

export interface PropertiesEntry {
  key: string;
  value: string;
  comment?: string;
}

export type ConversionDirection = 'properties-to-yaml' | 'yaml-to-properties';
