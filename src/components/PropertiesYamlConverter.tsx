import React, { useState } from 'react';
import { ArrowLeftRight, CheckCircle, Copy, Download, FileText, RotateCcw, AlertCircle } from 'lucide-react';
import { PropertiesYamlConverter } from '../utils/propertiesYamlConverter';
import { ConversionDirection, ConversionResult } from '../types';

export const PropertiesYamlConverterComponent: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [direction, setDirection] = useState<ConversionDirection>('properties-to-yaml');
  const [isConverting, setIsConverting] = useState(false);
  const [conversionResult, setConversionResult] = useState<ConversionResult | null>(null);

  const converter = new PropertiesYamlConverter();

  const handleConvert = async () => {
    if (!inputText.trim()) {
      alert('请输入要转换的内容');
      return;
    }

    setIsConverting(true);

    try {
      let result: ConversionResult;
      
      if (direction === 'properties-to-yaml') {
        result = converter.propertiesToYaml(inputText);
      } else {
        result = converter.yamlToProperties(inputText);
      }

      setConversionResult(result);
      
      if (result.success && result.result) {
        setOutputText(result.result);
      } else {
        setOutputText('');
      }
    } catch (error) {
      console.error('转换时出错:', error);
      setConversionResult({
        success: false,
        error: '转换时出现未知错误'
      });
      setOutputText('');
    } finally {
      setIsConverting(false);
    }
  };

  const handleSwapDirection = () => {
    const newDirection: ConversionDirection = 
      direction === 'properties-to-yaml' ? 'yaml-to-properties' : 'properties-to-yaml';
    
    setDirection(newDirection);
    
    // 交换输入输出内容
    const tempInput = inputText;
    setInputText(outputText);
    setOutputText(tempInput);
    setConversionResult(null);
  };

  const handleLoadExample = () => {
    if (direction === 'properties-to-yaml') {
      setInputText(PropertiesYamlConverter.getExampleProperties());
    } else {
      setInputText(PropertiesYamlConverter.getExampleYaml());
    }
    setOutputText('');
    setConversionResult(null);
  };

  const handleReset = () => {
    setInputText('');
    setOutputText('');
    setConversionResult(null);
  };

  const handleCopyOutput = () => {
    if (outputText) {
      navigator.clipboard.writeText(outputText).then(() => {
        alert('结果已复制到剪贴板');
      });
    }
  };

  const handleDownloadOutput = () => {
    if (!outputText) return;

    const extension = direction === 'properties-to-yaml' ? 'yml' : 'properties';
    const filename = `converted.${extension}`;
    const mimeType = direction === 'properties-to-yaml' ? 'text/yaml' : 'text/plain';

    const blob = new Blob([outputText], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getInputLabel = () => {
    return direction === 'properties-to-yaml' ? 'Properties 输入' : 'YAML 输入';
  };

  const getOutputLabel = () => {
    return direction === 'properties-to-yaml' ? 'YAML 输出' : 'Properties 输出';
  };

  const getInputPlaceholder = () => {
    return direction === 'properties-to-yaml' 
      ? '请输入 Properties 格式内容...\n例如：\napp.name=My App\napp.version=1.0.0'
      : '请输入 YAML 格式内容...\n例如：\napp:\n  name: My App\n  version: 1.0.0';
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6 text-blue-600" />
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Properties ⇄ YAML 转换器
              </h1>
              <p className="text-sm text-gray-600">
                支持 Properties 和 YAML 格式的双向转换
              </p>
            </div>
          </div>

          {/* Direction Toggle */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">
              {direction === 'properties-to-yaml' ? 'Properties → YAML' : 'YAML → Properties'}
            </span>
            <button
              onClick={handleSwapDirection}
              className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              title="切换转换方向"
            >
              <ArrowLeftRight className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2 mt-4">
          <button
            onClick={handleConvert}
            disabled={isConverting || !inputText.trim()}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ArrowLeftRight className="w-4 h-4" />
            <span>{isConverting ? '转换中...' : '转换'}</span>
          </button>

          <button
            onClick={handleLoadExample}
            className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <FileText className="w-4 h-4" />
            <span>加载示例</span>
          </button>

          <button
            onClick={handleReset}
            className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <RotateCcw className="w-4 h-4" />
            <span>重置</span>
          </button>

          {outputText && (
            <>
              <button
                onClick={handleCopyOutput}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <Copy className="w-4 h-4" />
                <span>复制结果</span>
              </button>

              <button
                onClick={handleDownloadOutput}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>下载结果</span>
              </button>
            </>
          )}
        </div>

        {/* Status Message */}
        {conversionResult && (
          <div className={`mt-3 p-3 rounded-lg flex items-start space-x-2 ${
            conversionResult.success 
              ? 'bg-green-50 border border-green-200' 
              : 'bg-red-50 border border-red-200'
          }`}>
            {conversionResult.success ? (
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
            ) : (
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            )}
            <div className="flex-1">
              <p className={`text-sm font-medium ${
                conversionResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {conversionResult.success ? '转换成功' : '转换失败'}
              </p>
              {conversionResult.error && (
                <p className="text-sm text-red-700 mt-1">
                  {conversionResult.error}
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 p-4 flex flex-col lg:flex-row gap-4">
        {/* Input */}
        <div className="flex-1 flex flex-col">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {getInputLabel()}
          </label>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="flex-1 w-full border border-gray-300 rounded-lg px-3 py-2 text-sm font-mono focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            placeholder={getInputPlaceholder()}
          />
        </div>

        {/* Output */}
        <div className="flex-1 flex flex-col">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {getOutputLabel()}
          </label>
          <textarea
            value={outputText}
            readOnly
            className="flex-1 w-full border border-gray-300 rounded-lg px-3 py-2 text-sm font-mono bg-gray-50 resize-none"
            placeholder="转换结果将显示在这里..."
          />
        </div>
      </div>
    </div>
  );
};
