import { SQLPrefixResult, BatchSQLResult, MultipleDatabaseResult } from '../types';

/**
 * 为SQL语句中的表名添加数据库前缀
 * 基于PHP版本的逻辑转换为TypeScript
 */
export class SQLPrefixer {
  // MySQL保留字和内置函数列表
  private readonly mysqlReservedWords = new Set([
    // 时间函数
    'CURRENT_TIMESTAMP', 'CURRENT_TIME', 'CURRENT_DATE', 'NOW', 'CURDATE', 'CURTIME',
    'LOCALTIME', 'LOCALTIMESTAMP', 'UTC_DATE', 'UTC_TIME', 'UTC_TIMESTAMP',
    // 数学函数
    'ABS', 'CEIL', 'CEILING', 'FLOOR', 'ROUND', 'TRUNCATE', 'MOD', 'POWER', 'SQRT',
    'RAND', 'PI', 'SIN', 'COS', 'TAN', 'ASIN', 'ACOS', 'ATAN', 'DEGREES', 'RADIANS',
    // 字符串函数
    'CONCAT', 'LENGTH', 'CHAR_LENGTH', 'CHARACTER_LENGTH', 'UPPER', 'LOWER', 'TRIM',
    'LTRIM', 'RTRIM', 'SUBSTRING', 'SUBSTR', 'LEFT', 'RIGHT', 'REPLACE', 'REVERSE',
    'LOCATE', 'POSITION', 'INSTR', 'LPAD', 'RPAD', 'REPEAT', 'SPACE',
    // 聚合函数
    'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'GROUP_CONCAT', 'STDDEV', 'VARIANCE',
    // 条件函数
    'IF', 'IFNULL', 'NULLIF', 'COALESCE', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
    // 类型转换函数
    'CAST', 'CONVERT', 'BINARY', 'CHAR', 'DATE', 'DATETIME', 'DECIMAL', 'SIGNED', 'UNSIGNED',
    // 其他常用函数
    'NULL', 'TRUE', 'FALSE', 'DEFAULT', 'AUTO_INCREMENT', 'PRIMARY', 'KEY', 'UNIQUE',
    'INDEX', 'FOREIGN', 'REFERENCES', 'CHECK', 'CONSTRAINT', 'ENGINE', 'CHARSET',
    'COLLATE', 'COMMENT', 'USING', 'BTREE', 'HASH', 'FULLTEXT', 'SPATIAL'
  ]);

  constructor() {
    // 构造函数现在不需要参数
  }

  /**
   * 为单个SQL语句添加数据库前缀
   */
  addDatabasePrefix(sql: string, dbName: string): SQLPrefixResult {
    const tablesFound: string[] = [];

    // 预处理SQL：移除多余的空白字符，但保持基本结构
    let normalizedSql = sql.replace(/\s+/g, ' ').trim();

    // 先保护SQL中的函数和保留字，避免被误处理
    const protectedTokens = new Map<string, string>();
    let tokenCounter = 0;

    // 保护 ON UPDATE CURRENT_TIMESTAMP 等常见模式
    const protectionPatterns = [
      /\bON\s+UPDATE\s+CURRENT_TIMESTAMP\b/gi,
      /\bDEFAULT\s+CURRENT_TIMESTAMP\b/gi,
      /\bDEFAULT\s+NULL\b/gi,
      /\bAUTO_INCREMENT\b/gi,
      /\bPRIMARY\s+KEY\b/gi,
      /\bUNIQUE\s+KEY\b/gi,
      /\bFOREIGN\s+KEY\b/gi,
      /\bUSING\s+BTREE\b/gi,
      /\bUSING\s+HASH\b/gi,
      /\bCHARACTER\s+SET\b/gi,
      /\bCOLLATE\s+\w+/gi,
      /\bENGINE\s*=\s*\w+/gi,
      /\bCHARSET\s*=\s*\w+/gi,
      /\bROW_FORMAT\s*=\s*\w+/gi,
      /\bAUTO_INCREMENT\s*=\s*\d+/gi
    ];

    protectionPatterns.forEach(pattern => {
      normalizedSql = normalizedSql.replace(pattern, (match) => {
        const token = `__PROTECTED_TOKEN_${tokenCounter++}__`;
        protectedTokens.set(token, match);
        return token;
      });
    });

    // 改进的正则表达式模式，支持多行和复杂语句
    const patterns = [
      // 匹配反引号包围的表名 - 改进版，支持多行
      {
        pattern: /\b(FROM|JOIN|INTO|UPDATE|ALTER\s+TABLE|CREATE\s+TABLE(?:\s+IF\s+NOT\s+EXISTS)?)\s+`([^`]+)`/gi,
        quoteType: 'backtick'
      },
      // 匹配双引号包围的表名
      {
        pattern: /\b(FROM|JOIN|INTO|UPDATE|ALTER\s+TABLE|CREATE\s+TABLE(?:\s+IF\s+NOT\s+EXISTS)?)\s+"([^"]+)"/gi,
        quoteType: 'double'
      },
      // 匹配方括号包围的表名 (SQL Server)
      {
        pattern: /\b(FROM|JOIN|INTO|UPDATE|ALTER\s+TABLE|CREATE\s+TABLE(?:\s+IF\s+NOT\s+EXISTS)?)\s+\[([^\]]+)\]/gi,
        quoteType: 'bracket'
      },
      // 匹配普通表名（无引号）- 改进版，更精确的匹配
      {
        pattern: /\b(FROM|JOIN|INTO|UPDATE|ALTER\s+TABLE|CREATE\s+TABLE(?:\s+IF\s+NOT\s+EXISTS)?)\s+([a-zA-Z_][a-zA-Z0-9_]*)\b(?!\s*\()/gi,
        quoteType: 'none'
      }
    ];

    let modifiedSql = normalizedSql;

    // 按顺序应用每个模式
    patterns.forEach(({ pattern, quoteType }) => {
      modifiedSql = modifiedSql.replace(pattern, (match, keyword, tableName) => {
        // 检查表名是否已经有数据库前缀，以及是否为保护的token
        if (!tableName.includes('.') &&
            !this.isReservedWord(tableName) &&
            !tableName.startsWith('__PROTECTED_TOKEN_')) {
          tablesFound.push(tableName);

          // 根据原始格式保持引号样式
          switch (quoteType) {
            case 'backtick':
              return `${keyword} \`${dbName}\`.\`${tableName}\``;
            case 'double':
              return `${keyword} "${dbName}"."${tableName}"`;
            case 'bracket':
              return `${keyword} [${dbName}].[${tableName}]`;
            case 'none':
            default:
              return `${keyword} ${dbName}.${tableName}`;
          }
        }
        return match;
      });
    });

    // 如果没有找到表名，尝试更宽松的匹配（针对复杂的CREATE TABLE语句）
    if (tablesFound.length === 0 && this.detectSQLType(sql) === 'CREATE') {
      const createTableMatch = this.extractCreateTableName(modifiedSql);
      if (createTableMatch) {
        tablesFound.push(createTableMatch.tableName);
        modifiedSql = this.replaceCreateTableName(modifiedSql, createTableMatch, dbName);
      }
    }

    // 恢复被保护的tokens
    protectedTokens.forEach((originalValue, token) => {
      modifiedSql = modifiedSql.replace(new RegExp(token, 'g'), originalValue);
    });

    const sqlType = this.detectSQLType(sql);
    const isModifying = this.isModifyingStatement(sql);

    return {
      original: sql,
      modified: modifiedSql,
      tablesFound: [...new Set(tablesFound)], // 去重
      sqlType,
      isModifying
    };
  }

  /**
   * 专门用于提取CREATE TABLE语句中的表名
   */
  private extractCreateTableName(sql: string): { tableName: string; quoteType: string; fullMatch: string } | null {
    // 匹配各种CREATE TABLE格式
    const patterns = [
      // CREATE TABLE `tablename` (
      { pattern: /CREATE\s+TABLE(?:\s+IF\s+NOT\s+EXISTS)?\s+`([^`]+)`\s*\(/i, quoteType: 'backtick' },
      // CREATE TABLE "tablename" (
      { pattern: /CREATE\s+TABLE(?:\s+IF\s+NOT\s+EXISTS)?\s+"([^"]+)"\s*\(/i, quoteType: 'double' },
      // CREATE TABLE [tablename] (
      { pattern: /CREATE\s+TABLE(?:\s+IF\s+NOT\s+EXISTS)?\s+\[([^\]]+)\]\s*\(/i, quoteType: 'bracket' },
      // CREATE TABLE tablename (
      { pattern: /CREATE\s+TABLE(?:\s+IF\s+NOT\s+EXISTS)?\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/i, quoteType: 'none' }
    ];

    for (const { pattern, quoteType } of patterns) {
      const match = sql.match(pattern);
      if (match) {
        return {
          tableName: match[1],
          quoteType,
          fullMatch: match[0]
        };
      }
    }

    return null;
  }

  /**
   * 替换CREATE TABLE语句中的表名
   */
  private replaceCreateTableName(sql: string, tableMatch: { tableName: string; quoteType: string; fullMatch: string }, dbName: string): string {
    // 检查表名是否已经有数据库前缀
    if (tableMatch.tableName.includes('.')) {
      return sql;
    }

    let replacement: string;
    switch (tableMatch.quoteType) {
      case 'backtick':
        replacement = tableMatch.fullMatch.replace(`\`${tableMatch.tableName}\``, `\`${dbName}\`.\`${tableMatch.tableName}\``);
        break;
      case 'double':
        replacement = tableMatch.fullMatch.replace(`"${tableMatch.tableName}"`, `"${dbName}"."${tableMatch.tableName}"`);
        break;
      case 'bracket':
        replacement = tableMatch.fullMatch.replace(`[${tableMatch.tableName}]`, `[${dbName}].[${tableMatch.tableName}]`);
        break;
      case 'none':
      default:
        replacement = tableMatch.fullMatch.replace(tableMatch.tableName, `${dbName}.${tableMatch.tableName}`);
        break;
    }

    return sql.replace(tableMatch.fullMatch, replacement);
  }

  /**
   * 检查是否为MySQL保留字或内置函数
   */
  private isReservedWord(word: string): boolean {
    return this.mysqlReservedWords.has(word.toUpperCase());
  }

  /**
   * 判断SQL语句是否为修改类型（非查询）
   */
  private isModifyingStatement(sql: string): boolean {
    const sqlType = this.detectSQLType(sql);
    // 只有这些类型的语句才算"修改表"
    const modifyingTypes = ['INSERT', 'UPDATE', 'DELETE', 'ALTER', 'CREATE', 'DROP'];
    return modifyingTypes.includes(sqlType);
  }

  /**
   * 批量处理多个SQL语句
   */
  batchProcess(sqlStatements: string[], dbName: string): BatchSQLResult {
    const results: SQLPrefixResult[] = [];
    let totalTablesModified = 0;
    let queryStatements = 0;
    let modifyStatements = 0;

    sqlStatements.forEach(sql => {
      const result = this.addDatabasePrefix(sql.trim(), dbName);
      results.push(result);

      // 统计语句类型
      if (result.isModifying) {
        modifyStatements++;
        // 只有修改类型的语句才计入"表被修改"统计
        totalTablesModified += result.tablesFound.length;
      } else {
        queryStatements++;
      }
    });

    return {
      results,
      summary: {
        total: sqlStatements.length,
        processed: results.length,
        tablesModified: totalTablesModified,
        queryStatements,
        modifyStatements
      }
    };
  }

  /**
   * 从文本中提取SQL语句（智能分割，处理复杂语句）
   */
  extractSQLStatements(text: string): string[] {
    // 使用更智能的SQL分割逻辑
    return this.smartSQLSplit(text)
      .map(sql => sql.trim())
      .filter(sql => sql.length > 0);
  }

  /**
   * 智能SQL分割 - 正确处理字符串、注释和复杂语句结构
   */
  private smartSQLSplit(text: string): string[] {
    const statements: string[] = [];
    let currentStatement = '';
    let inSingleQuote = false;
    let inDoubleQuote = false;
    let inBacktick = false;
    let inLineComment = false;
    let inBlockComment = false;
    let parenthesesLevel = 0;

    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const nextChar = text[i + 1];
      const prevChar = text[i - 1];

      // 处理行注释
      if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inBlockComment) {
        if (char === '-' && nextChar === '-') {
          inLineComment = true;
          currentStatement += char;
          continue;
        }
      }

      // 行注释结束
      if (inLineComment && (char === '\n' || char === '\r')) {
        inLineComment = false;
        currentStatement += char;
        continue;
      }

      // 处理块注释
      if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment) {
        if (char === '/' && nextChar === '*') {
          inBlockComment = true;
          currentStatement += char;
          continue;
        }
      }

      // 块注释结束
      if (inBlockComment && char === '*' && nextChar === '/') {
        inBlockComment = false;
        currentStatement += char + nextChar;
        i++; // 跳过下一个字符
        continue;
      }

      // 如果在注释中，直接添加字符
      if (inLineComment || inBlockComment) {
        currentStatement += char;
        continue;
      }

      // 处理字符串引号
      if (char === "'" && !inDoubleQuote && !inBacktick) {
        // 检查是否是转义的单引号
        if (prevChar !== '\\') {
          inSingleQuote = !inSingleQuote;
        }
      } else if (char === '"' && !inSingleQuote && !inBacktick) {
        // 检查是否是转义的双引号
        if (prevChar !== '\\') {
          inDoubleQuote = !inDoubleQuote;
        }
      } else if (char === '`' && !inSingleQuote && !inDoubleQuote) {
        // 检查是否是转义的反引号
        if (prevChar !== '\\') {
          inBacktick = !inBacktick;
        }
      }

      // 处理括号层级（用于CREATE TABLE等复杂语句）
      if (!inSingleQuote && !inDoubleQuote && !inBacktick) {
        if (char === '(') {
          parenthesesLevel++;
        } else if (char === ')') {
          parenthesesLevel--;
        }
      }

      // 处理分号 - 只有在不在引号内、不在注释内、括号平衡时才作为语句分隔符
      if (char === ';' && !inSingleQuote && !inDoubleQuote && !inBacktick && parenthesesLevel === 0) {
        // 添加当前语句（包含分号）
        currentStatement += char;
        const trimmedStatement = currentStatement.trim();
        if (trimmedStatement.length > 0) {
          statements.push(trimmedStatement);
        }
        currentStatement = '';
      } else {
        currentStatement += char;
      }
    }

    // 添加最后一个语句（如果没有以分号结尾）
    const trimmedStatement = currentStatement.trim();
    if (trimmedStatement.length > 0) {
      statements.push(trimmedStatement);
    }

    return statements;
  }

  /**
   * 验证数据库名称格式
   */
  validateDatabaseName(dbName: string): boolean {
    const dbNamePattern = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
    return dbNamePattern.test(dbName);
  }

  /**
   * 获取预定义的数据库名示例
   */
  static getDefaultDatabaseNames(): string[] {
    return [
      'database1',
      'database2',
      'production_db',
      'development_db'
    ];
  }

  /**
   * 检测SQL语句类型
   */
  detectSQLType(sql: string): string {
    const upperSQL = sql.trim().toUpperCase();

    if (upperSQL.startsWith('SELECT')) return 'SELECT';
    if (upperSQL.startsWith('INSERT')) return 'INSERT';
    if (upperSQL.startsWith('UPDATE')) return 'UPDATE';
    if (upperSQL.startsWith('DELETE')) return 'DELETE';
    if (upperSQL.startsWith('ALTER')) return 'ALTER';
    if (upperSQL.startsWith('CREATE')) return 'CREATE';
    if (upperSQL.startsWith('DROP')) return 'DROP';

    return 'UNKNOWN';
  }

  /**
   * 为多个数据库批量处理SQL语句
   */
  processMultipleDatabases(sqlStatements: string[], databaseNames: string[]): MultipleDatabaseResult {
    const result: MultipleDatabaseResult = {};

    databaseNames.forEach(dbName => {
      if (!this.validateDatabaseName(dbName)) {
        console.warn(`跳过无效的数据库名: ${dbName}`);
        return;
      }

      const batchResult = this.batchProcess(sqlStatements, dbName);
      result[dbName] = batchResult;
    });

    return result;
  }

  /**
   * 从文本中解析数据库名列表（支持多种分隔符）
   */
  parseDatabaseNames(text: string): string[] {
    return text
      .split(/[,;\n\r\t\s]+/) // 支持逗号、分号、换行、制表符、空格分隔
      .map(name => name.trim())
      .filter(name => name.length > 0)
      .filter((name, index, arr) => arr.indexOf(name) === index); // 去重
  }
}

// 导出默认实例
export const sqlPrefixer = new SQLPrefixer();
