import * as yaml from 'js-yaml';
import { ConversionResult, PropertiesEntry } from '../types';

export class PropertiesYamlConverter {
  /**
   * 将Properties格式转换为YAML格式
   */
  propertiesToYaml(propertiesText: string): ConversionResult {
    try {
      if (!propertiesText.trim()) {
        return {
          success: false,
          error: 'Properties内容不能为空'
        };
      }

      const propertiesObject = this.parseProperties(propertiesText);
      const yamlResult = yaml.dump(propertiesObject, {
        indent: 2,
        lineWidth: -1,
        noRefs: true,
        sortKeys: false
      });

      return {
        success: true,
        result: yamlResult
      };
    } catch (error) {
      return {
        success: false,
        error: `转换失败: ${(error as Error).message}`
      };
    }
  }

  /**
   * 将YAML格式转换为Properties格式
   */
  yamlToProperties(yamlText: string): ConversionResult {
    try {
      if (!yamlText.trim()) {
        return {
          success: false,
          error: 'YAML内容不能为空'
        };
      }

      const yamlObject = yaml.load(yamlText);
      if (yamlObject === null || yamlObject === undefined) {
        return {
          success: false,
          error: 'YAML内容解析为空'
        };
      }

      const propertiesResult = this.objectToProperties(yamlObject);

      return {
        success: true,
        result: propertiesResult
      };
    } catch (error) {
      return {
        success: false,
        error: `转换失败: ${(error as Error).message}`
      };
    }
  }

  /**
   * 解析Properties格式文本为对象
   */
  private parseProperties(propertiesText: string): Record<string, any> {
    const result: Record<string, any> = {};
    const lines = propertiesText.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 跳过空行和注释行
      if (!line || line.startsWith('#') || line.startsWith('!')) {
        continue;
      }

      // 查找等号或冒号分隔符
      const separatorIndex = this.findSeparator(line);
      if (separatorIndex === -1) {
        continue; // 跳过无效行
      }

      const key = line.substring(0, separatorIndex).trim();
      let value = line.substring(separatorIndex + 1).trim();

      // 处理转义字符
      value = this.unescapeValue(value);

      // 处理嵌套属性（如 app.database.host）
      this.setNestedProperty(result, key, value);
    }

    return result;
  }

  /**
   * 查找属性分隔符（= 或 :）
   */
  private findSeparator(line: string): number {
    let inEscape = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (inEscape) {
        inEscape = false;
        continue;
      }
      
      if (char === '\\') {
        inEscape = true;
        continue;
      }
      
      if (char === '=' || char === ':') {
        return i;
      }
    }
    
    return -1;
  }

  /**
   * 处理转义字符
   */
  private unescapeValue(value: string): string {
    return value
      .replace(/\\n/g, '\n')
      .replace(/\\r/g, '\r')
      .replace(/\\t/g, '\t')
      .replace(/\\\\/g, '\\')
      .replace(/\\=/g, '=')
      .replace(/\\:/g, ':')
      .replace(/\\#/g, '#')
      .replace(/\\!/g, '!');
  }

  /**
   * 设置嵌套属性
   */
  private setNestedProperty(obj: Record<string, any>, key: string, value: string): void {
    const keys = key.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in current) || typeof current[k] !== 'object' || Array.isArray(current[k])) {
        current[k] = {};
      }
      current = current[k];
    }

    const finalKey = keys[keys.length - 1];
    
    // 尝试转换为适当的类型
    current[finalKey] = this.convertValue(value);
  }

  /**
   * 转换值为适当的类型
   */
  private convertValue(value: string): any {
    // 布尔值
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;
    
    // 数字
    if (/^-?\d+$/.test(value)) {
      return parseInt(value, 10);
    }
    
    if (/^-?\d*\.\d+$/.test(value)) {
      return parseFloat(value);
    }
    
    // 字符串
    return value;
  }

  /**
   * 将对象转换为Properties格式
   */
  private objectToProperties(obj: any, prefix: string = ''): string {
    const lines: string[] = [];
    
    if (obj === null || obj === undefined) {
      return '';
    }
    
    if (typeof obj !== 'object' || Array.isArray(obj)) {
      // 如果是基本类型或数组，直接返回
      return prefix ? `${prefix}=${this.escapeValue(String(obj))}` : String(obj);
    }

    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (value === null || value === undefined) {
        lines.push(`${fullKey}=`);
      } else if (typeof value === 'object' && !Array.isArray(value)) {
        // 递归处理嵌套对象
        const nestedProperties = this.objectToProperties(value, fullKey);
        if (nestedProperties) {
          lines.push(nestedProperties);
        }
      } else {
        // 基本类型或数组
        lines.push(`${fullKey}=${this.escapeValue(String(value))}`);
      }
    }

    return lines.join('\n');
  }

  /**
   * 转义Properties值
   */
  private escapeValue(value: string): string {
    return value
      .replace(/\\/g, '\\\\')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t')
      .replace(/=/g, '\\=')
      .replace(/:/g, '\\:')
      .replace(/#/g, '\\#')
      .replace(/!/g, '\\!');
  }

  /**
   * 验证Properties格式
   */
  validateProperties(propertiesText: string): { isValid: boolean; error?: string } {
    try {
      this.parseProperties(propertiesText);
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 验证YAML格式
   */
  validateYaml(yamlText: string): { isValid: boolean; error?: string } {
    try {
      yaml.load(yamlText);
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 获取示例Properties内容
   */
  static getExampleProperties(): string {
    return `# 应用配置
app.name=My Application
app.version=1.0.0
app.debug=true

# 数据库配置
database.host=localhost
database.port=3306
database.name=mydb
database.username=admin
database.password=secret123

# 服务器配置
server.port=8080
server.ssl.enabled=false
server.timeout=30000

# 日志配置
logging.level.root=INFO
logging.level.com.example=DEBUG
logging.file.name=app.log`;
  }

  /**
   * 获取示例YAML内容
   */
  static getExampleYaml(): string {
    return `# 应用配置
app:
  name: My Application
  version: 1.0.0
  debug: true

# 数据库配置
database:
  host: localhost
  port: 3306
  name: mydb
  username: admin
  password: secret123

# 服务器配置
server:
  port: 8080
  ssl:
    enabled: false
  timeout: 30000

# 日志配置
logging:
  level:
    root: INFO
    com.example: DEBUG
  file:
    name: app.log`;
  }
}

// 导出默认实例
export const propertiesYamlConverter = new PropertiesYamlConverter();
