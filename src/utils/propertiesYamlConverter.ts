import * as yaml from 'js-yaml';
import { ConversionResult, PropertiesEntry } from '../types';

interface ParsedProperty {
  key: string;
  value: any;
  comment?: string;
  section?: string;
}

interface PropertySection {
  comment?: string;
  properties: ParsedProperty[];
}

export class PropertiesYamlConverter {
  /**
   * 将Properties格式转换为YAML格式
   */
  propertiesToYaml(propertiesText: string): ConversionResult {
    try {
      if (!propertiesText.trim()) {
        return {
          success: false,
          error: 'Properties内容不能为空'
        };
      }

      const parsedData = this.parsePropertiesWithComments(propertiesText);
      const yamlResult = this.generateYamlWithComments(parsedData);

      return {
        success: true,
        result: yamlResult
      };
    } catch (error) {
      return {
        success: false,
        error: `转换失败: ${(error as Error).message}`
      };
    }
  }

  /**
   * 将YAML格式转换为Properties格式
   */
  yamlToProperties(yamlText: string): ConversionResult {
    try {
      if (!yamlText.trim()) {
        return {
          success: false,
          error: 'YAML内容不能为空'
        };
      }

      const parsedData = this.parseYamlWithComments(yamlText);
      const propertiesResult = this.generatePropertiesWithComments(parsedData);

      return {
        success: true,
        result: propertiesResult
      };
    } catch (error) {
      return {
        success: false,
        error: `转换失败: ${(error as Error).message}`
      };
    }
  }

  /**
   * 解析Properties格式文本为对象
   */
  private parseProperties(propertiesText: string): Record<string, any> {
    const result: Record<string, any> = {};
    const lines = propertiesText.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 跳过空行和注释行
      if (!line || line.startsWith('#') || line.startsWith('!')) {
        continue;
      }

      // 查找等号或冒号分隔符
      const separatorIndex = this.findSeparator(line);
      if (separatorIndex === -1) {
        continue; // 跳过无效行
      }

      const key = line.substring(0, separatorIndex).trim();
      let value = line.substring(separatorIndex + 1).trim();

      // 处理转义字符
      value = this.unescapeValue(value);

      // 处理嵌套属性（如 app.database.host）
      this.setNestedProperty(result, key, value);
    }

    return result;
  }

  /**
   * 查找属性分隔符（= 或 :）
   */
  private findSeparator(line: string): number {
    let inEscape = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (inEscape) {
        inEscape = false;
        continue;
      }
      
      if (char === '\\') {
        inEscape = true;
        continue;
      }
      
      if (char === '=' || char === ':') {
        return i;
      }
    }
    
    return -1;
  }

  /**
   * 处理转义字符
   */
  private unescapeValue(value: string): string {
    return value
      .replace(/\\n/g, '\n')
      .replace(/\\r/g, '\r')
      .replace(/\\t/g, '\t')
      .replace(/\\\\/g, '\\')
      .replace(/\\=/g, '=')
      .replace(/\\:/g, ':')
      .replace(/\\#/g, '#')
      .replace(/\\!/g, '!');
  }

  /**
   * 设置嵌套属性
   */
  private setNestedProperty(obj: Record<string, any>, key: string, value: string): void {
    const keys = key.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in current) || typeof current[k] !== 'object' || Array.isArray(current[k])) {
        current[k] = {};
      }
      current = current[k];
    }

    const finalKey = keys[keys.length - 1];
    
    // 尝试转换为适当的类型
    current[finalKey] = this.convertValue(value);
  }

  /**
   * 转换值为适当的类型
   */
  private convertValue(value: any): any {
    // 如果不是字符串，直接返回
    if (typeof value !== 'string') {
      return value;
    }

    // 布尔值
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;

    // 数字
    if (/^-?\d+$/.test(value)) {
      return parseInt(value, 10);
    }

    if (/^-?\d*\.\d+$/.test(value)) {
      return parseFloat(value);
    }

    // 字符串
    return value;
  }

  /**
   * 将对象转换为Properties格式
   */
  private objectToProperties(obj: any, prefix: string = ''): string {
    const lines: string[] = [];
    
    if (obj === null || obj === undefined) {
      return '';
    }
    
    if (typeof obj !== 'object' || Array.isArray(obj)) {
      // 如果是基本类型或数组，直接返回
      return prefix ? `${prefix}=${this.escapeValue(String(obj))}` : String(obj);
    }

    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (value === null || value === undefined) {
        lines.push(`${fullKey}=`);
      } else if (typeof value === 'object' && !Array.isArray(value)) {
        // 递归处理嵌套对象
        const nestedProperties = this.objectToProperties(value, fullKey);
        if (nestedProperties) {
          lines.push(nestedProperties);
        }
      } else {
        // 基本类型或数组
        lines.push(`${fullKey}=${this.escapeValue(String(value))}`);
      }
    }

    return lines.join('\n');
  }

  /**
   * 转义Properties值
   */
  private escapeValue(value: any): string {
    const stringValue = String(value);
    return stringValue
      .replace(/\\/g, '\\\\')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t')
      .replace(/=/g, '\\=')
      .replace(/:/g, '\\:')
      .replace(/#/g, '\\#')
      .replace(/!/g, '\\!');
  }

  /**
   * 验证Properties格式
   */
  validateProperties(propertiesText: string): { isValid: boolean; error?: string } {
    try {
      this.parseProperties(propertiesText);
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 验证YAML格式
   */
  validateYaml(yamlText: string): { isValid: boolean; error?: string } {
    try {
      yaml.load(yamlText);
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 获取示例Properties内容
   */
  static getExampleProperties(): string {
    return `# 应用配置
app.name=My Application
app.version=1.0.0
app.debug=true

# 数据库配置
database.host=localhost
database.port=3306
database.name=mydb
database.username=admin
database.password=secret123

# 服务器配置
server.port=8080
server.ssl.enabled=false
server.timeout=30000

# 日志配置
logging.level.root=INFO
logging.level.com.example=DEBUG
logging.file.name=app.log`;
  }

  /**
   * 获取示例YAML内容
   */
  static getExampleYaml(): string {
    return `# 应用配置
app:
  name: My Application
  version: 1.0.0
  debug: true

# 数据库配置
database:
  host: localhost
  port: 3306
  name: mydb
  username: admin
  password: secret123

# 服务器配置
server:
  port: 8080
  ssl:
    enabled: false
  timeout: 30000

# 日志配置
logging:
  level:
    root: INFO
    com.example: DEBUG
  file:
    name: app.log`;
  }

  /**
   * 解析Properties文本，保留注释信息
   */
  private parsePropertiesWithComments(propertiesText: string): PropertySection[] {
    const sections: PropertySection[] = [];
    const lines = propertiesText.split('\n');
    let currentSection: PropertySection = { properties: [] };
    let pendingComment: string | undefined;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // 处理注释行
      if (trimmedLine.startsWith('#') || trimmedLine.startsWith('!')) {
        const comment = trimmedLine.substring(1).trim();

        // 如果当前section已有属性，则开始新section
        if (currentSection.properties.length > 0) {
          sections.push(currentSection);
          currentSection = { properties: [] };
        }

        // 设置section注释或待处理注释
        if (!currentSection.comment) {
          currentSection.comment = comment;
        } else {
          pendingComment = comment;
        }
        continue;
      }

      // 跳过空行
      if (!trimmedLine) {
        continue;
      }

      // 处理属性行
      const separatorIndex = this.findSeparator(trimmedLine);
      if (separatorIndex !== -1) {
        const key = trimmedLine.substring(0, separatorIndex).trim();
        let value = trimmedLine.substring(separatorIndex + 1).trim();
        value = this.unescapeValue(value);

        const property: ParsedProperty = {
          key,
          value: this.convertValue(value),
          comment: pendingComment
        };

        currentSection.properties.push(property);
        pendingComment = undefined;
      }
    }

    // 添加最后一个section
    if (currentSection.properties.length > 0 || currentSection.comment) {
      sections.push(currentSection);
    }

    return sections;
  }

  /**
   * 生成带注释的YAML
   */
  private generateYamlWithComments(sections: PropertySection[]): string {
    const result: string[] = [];

    for (const section of sections) {
      // 添加section注释
      if (section.comment) {
        result.push(`# ${section.comment}`);
      }

      // 构建该section的对象
      const sectionObject: Record<string, any> = {};
      for (const prop of section.properties) {
        this.setNestedProperty(sectionObject, prop.key, prop.value);
      }

      // 生成YAML并添加属性注释
      const yamlLines = yaml.dump(sectionObject, {
        indent: 2,
        lineWidth: -1,
        noRefs: true,
        sortKeys: false
      }).split('\n').filter(line => line.trim());

      // 为有注释的属性添加行内注释
      for (let i = 0; i < yamlLines.length; i++) {
        const line = yamlLines[i];
        const property = this.findPropertyForYamlLine(line, section.properties);

        if (property && property.comment) {
          yamlLines[i] = `${line}  # ${property.comment}`;
        }
      }

      result.push(...yamlLines);
      result.push(''); // 添加空行分隔sections
    }

    // 移除最后的空行
    while (result.length > 0 && result[result.length - 1] === '') {
      result.pop();
    }

    return result.join('\n');
  }

  /**
   * 查找YAML行对应的属性
   */
  private findPropertyForYamlLine(yamlLine: string, properties: ParsedProperty[]): ParsedProperty | undefined {
    const trimmed = yamlLine.trim();

    for (const prop of properties) {
      // 简单匹配：检查属性键是否在YAML行中
      const keyParts = prop.key.split('.');
      const lastKey = keyParts[keyParts.length - 1];

      if (trimmed.startsWith(`${lastKey}:`)) {
        return prop;
      }
    }

    return undefined;
  }

  /**
   * 解析YAML文本，保留注释信息
   */
  private parseYamlWithComments(yamlText: string): PropertySection[] {
    const sections: PropertySection[] = [];
    const lines = yamlText.split('\n');
    let currentSection: PropertySection = { properties: [] };

    // 首先解析YAML对象
    const yamlObject = yaml.load(yamlText);
    const flatProperties = this.flattenObject(yamlObject);

    // 然后解析注释
    let pendingComment: string | undefined;
    let currentSectionComment: string | undefined;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();

      // 处理注释
      if (trimmed.startsWith('#')) {
        const comment = trimmed.substring(1).trim();

        // 检查下一个非空行是否是顶级属性（section开始）
        let nextNonEmptyLine = '';
        for (let j = i + 1; j < lines.length; j++) {
          const nextLine = lines[j].trim();
          if (nextLine && !nextLine.startsWith('#')) {
            nextNonEmptyLine = nextLine;
            break;
          }
        }

        // 如果下一行是顶级属性（不以空格开头），则这是section注释
        if (nextNonEmptyLine && !nextNonEmptyLine.startsWith(' ') && nextNonEmptyLine.includes(':')) {
          if (currentSection.properties.length > 0) {
            sections.push(currentSection);
            currentSection = { properties: [] };
          }
          currentSectionComment = comment;
        } else {
          // 否则是属性注释
          pendingComment = comment;
        }
        continue;
      }

      // 处理属性行
      if (trimmed && trimmed.includes(':') && !trimmed.startsWith(' ')) {
        // 这是一个新的顶级属性，开始新section
        if (currentSection.properties.length > 0) {
          sections.push(currentSection);
          currentSection = { properties: [] };
        }

        if (currentSectionComment) {
          currentSection.comment = currentSectionComment;
          currentSectionComment = undefined;
        }
      }

      // 查找匹配的扁平化属性
      if (trimmed && trimmed.includes(':')) {
        const matchingProps = this.findMatchingPropsForYamlLine(line, flatProperties);
        for (const prop of matchingProps) {
          const property: ParsedProperty = {
            key: prop.key,
            value: prop.value,
            comment: pendingComment
          };

          currentSection.properties.push(property);
          pendingComment = undefined;
          break; // 只处理第一个匹配的属性
        }
      }
    }

    // 添加最后一个section
    if (currentSection.properties.length > 0 || currentSection.comment) {
      sections.push(currentSection);
    }

    return sections;
  }

  /**
   * 扁平化对象为键值对
   */
  private flattenObject(obj: any, prefix: string = ''): Array<{key: string, value: any}> {
    const result: Array<{key: string, value: any}> = [];

    if (obj === null || obj === undefined) {
      return result;
    }

    if (typeof obj !== 'object' || Array.isArray(obj)) {
      if (prefix) {
        result.push({ key: prefix, value: obj });
      }
      return result;
    }

    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;

      if (value === null || value === undefined) {
        result.push({ key: fullKey, value: '' });
      } else if (typeof value === 'object' && !Array.isArray(value)) {
        result.push(...this.flattenObject(value, fullKey));
      } else {
        result.push({ key: fullKey, value });
      }
    }

    return result;
  }

  /**
   * 查找YAML行对应的属性
   */
  private findMatchingPropsForYamlLine(yamlLine: string, flatProps: Array<{key: string, value: any}>): Array<{key: string, value: any}> {
    const trimmed = yamlLine.trim();
    const colonIndex = trimmed.indexOf(':');

    if (colonIndex === -1) return [];

    const yamlKey = trimmed.substring(0, colonIndex).trim();
    const yamlValue = trimmed.substring(colonIndex + 1).trim();

    // 查找匹配的属性
    return flatProps.filter(prop => {
      const keyParts = prop.key.split('.');
      const lastKey = keyParts[keyParts.length - 1];

      // 匹配键名和值
      return lastKey === yamlKey && String(prop.value) === yamlValue;
    });
  }

  /**
   * 生成带注释的Properties
   */
  private generatePropertiesWithComments(sections: PropertySection[]): string {
    const result: string[] = [];

    for (let i = 0; i < sections.length; i++) {
      const section = sections[i];

      // 添加section注释
      if (section.comment) {
        result.push(`# ${section.comment}`);
      }

      // 添加属性
      for (const prop of section.properties) {
        if (prop.comment) {
          result.push(`# ${prop.comment}`);
        }
        result.push(`${prop.key}=${this.escapeValue(String(prop.value))}`);
      }

      // 在sections之间添加空行（除了最后一个）
      if (i < sections.length - 1) {
        result.push('');
      }
    }

    return result.join('\n');
  }
}

// 导出默认实例
export const propertiesYamlConverter = new PropertiesYamlConverter();
