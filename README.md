# Dev Tools App

A comprehensive development tools application built with React, TypeScript, and Tailwind CSS. This application provides essential utilities for developers including database management, SQL processing, API testing, and JSON formatting.

## Features

### 🗄️ Database Manager
- Connect to multiple database types (SQLite, MySQL, PostgreSQL, MongoDB)
- Visual table browser with column information
- Interactive query editor with syntax highlighting
- Query execution with formatted results
- Connection management and persistence

### 🔧 SQL Processor
- Advanced SQL query editor with syntax validation
- Query formatting and beautification
- Query library with search and tagging
- SQL templates for common operations
- Query history and management
- Syntax highlighting and error detection

### 🌐 API Tester (Coming Soon)
- REST API testing interface
- Request/response management
- Authentication support
- Environment variables

### 📄 JSON Formatter (Coming Soon)
- JSON validation and formatting
- Tree view for complex structures
- Minify/beautify operations
- Schema validation

## Technology Stack

- **Frontend**: React 18 with TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Build Tool**: Vite
- **Development**: Hot reload, TypeScript checking

## Project Structure

```
dev-tools-app/
├── src/
│   ├── components/
│   │   ├── DatabaseManager.tsx    # Database connection and query interface
│   │   ├── SQLProcessor.tsx       # SQL editor and query management
│   │   └── ToolTabs.tsx          # Navigation tabs component
│   ├── hooks/
│   │   ├── useDatabase.ts        # Database operations hook
│   │   └── useSQLProcessor.ts    # SQL processing hook
│   ├── types/
│   │   └── index.ts              # TypeScript type definitions
│   ├── utils/
│   │   └── sqlProcessor.ts       # SQL utility functions
│   ├── App.tsx                   # Main application component
│   ├── main.tsx                  # Application entry point
│   └── index.css                 # Global styles and Tailwind imports
├── package.json                  # Dependencies and scripts
├── tailwind.config.js           # Tailwind CSS configuration
├── tsconfig.json                # TypeScript configuration
└── vite.config.ts               # Vite build configuration
```

## Getting Started

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation

1. Clone or download the project
2. Navigate to the project directory:
   ```bash
   cd dev-tools-app
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open your browser and navigate to `http://localhost:3000`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Usage

### Database Manager

1. **Add Connection**: Click the "Add" button to create a new database connection
2. **Connect**: Use the play button to establish connection to your database
3. **Browse Tables**: View table structure and row counts in the sidebar
4. **Execute Queries**: Write and run SQL queries in the editor
5. **View Results**: See query results in a formatted table

### SQL Processor

1. **Create Query**: Write SQL in the editor and give it a name
2. **Format**: Use the format button to beautify your SQL
3. **Validate**: Check SQL syntax for errors
4. **Save**: Store queries in your personal library
5. **Search**: Find saved queries using the search functionality
6. **Templates**: Use predefined SQL templates for common operations

## Features in Detail

### Database Connections
- Support for multiple database types
- Secure credential storage (localStorage)
- Connection status indicators
- Easy connection switching

### Query Management
- Persistent query storage
- Tagging and categorization
- Search and filter capabilities
- Query duplication and sharing
- Execution history

### SQL Processing
- Syntax highlighting
- Auto-formatting
- Error detection and validation
- Table extraction
- Keyword highlighting

## Data Storage

The application uses browser localStorage to persist:
- Database connections (credentials are stored locally)
- Saved SQL queries
- User preferences

## Security Notes

- Database credentials are stored in browser localStorage
- No data is sent to external servers
- All processing happens client-side
- Consider using environment variables for sensitive data in production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Roadmap

- [ ] API Testing module
- [ ] JSON Formatter module
- [ ] Export/Import functionality
- [ ] Dark mode support
- [ ] Query execution plans
- [ ] Database schema visualization
- [ ] Collaborative features
- [ ] Plugin system

## Support

For issues, questions, or contributions, please create an issue in the project repository.
