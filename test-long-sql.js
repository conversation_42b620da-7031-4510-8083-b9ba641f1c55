// 测试长SQL语句的处理 - 使用Node.js兼容的导入方式
// 注意：这是一个测试文件，需要先编译TypeScript

// 你提供的长CREATE TABLE语句
const longCreateTableSQL = `CREATE TABLE \`agency\` (
  \`agency_id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '服务商id',
  \`agency_code\` varchar(50) DEFAULT NULL COMMENT '服务商编码',
  \`agency_name\` varchar(50) DEFAULT NULL COMMENT '服务商名称',
  \`merchant_number\` varchar(255) DEFAULT NULL COMMENT '商户编号',
  \`agency_secret\` varchar(50) DEFAULT NULL COMMENT '服务商秘钥',
  \`agency_key\` varchar(30) DEFAULT NULL COMMENT '服务商唯一key',
  \`is_our_system\` char(1) DEFAULT NULL COMMENT '是否我们系统1：是2：不是',
  \`docking_way\` int(11) DEFAULT NULL COMMENT '服务商对接方式(1:系统 2:saas 3:电话)',
  \`credit_code\` varchar(50) DEFAULT NULL COMMENT '信用代码',
  \`institution_code\` varchar(255) DEFAULT NULL COMMENT '机构代码',
  \`customer_service_contacts\` varchar(50) DEFAULT NULL COMMENT '客服联系人',
  \`customer_service_phone_number\` varchar(20) DEFAULT NULL COMMENT '客服电话',
  \`business_contacts\` varchar(50) DEFAULT NULL COMMENT '业务联系人',
  \`business_phone_number\` varchar(20) DEFAULT NULL COMMENT '业务联系电话',
  \`disaster_contacts\` varchar(50) DEFAULT NULL COMMENT '灾备联系人',
  \`disaster_phone_number\` varchar(20) DEFAULT NULL COMMENT '灾备联系电话',
  \`settlement_contacts\` varchar(50) DEFAULT NULL COMMENT '结算联系人',
  \`settlement_phone_number\` varchar(20) DEFAULT NULL COMMENT '结算联系电话',
  \`customer_complaint_contacts\` varchar(50) DEFAULT NULL COMMENT '客诉联系人',
  \`customer_complaint_phone_number\` varchar(20) DEFAULT NULL COMMENT '客诉联系电话',
  \`cloud_call_phone_number\` varchar(20) DEFAULT NULL COMMENT '云呼电话',
  \`undertake_company\` varchar(500) DEFAULT NULL COMMENT '承接公司(数组)',
  \`real_com_id\` varchar(64) DEFAULT NULL COMMENT '本地承包公司id',
  \`address\` varchar(500) DEFAULT NULL COMMENT '服务商地址',
  \`billing_type\` int(11) DEFAULT NULL COMMENT '开票类型(1=增值税普通发票,2=增值税专用发票)',
  \`billing_company_name\` varchar(255) DEFAULT NULL COMMENT '公司名称(发票信息)',
  \`billing_company_address\` varchar(255) DEFAULT NULL COMMENT '公司地址(发票信息)',
  \`billing_company_phone\` varchar(20) DEFAULT NULL COMMENT '公司电话(发票信息)',
  \`cnaps\` varchar(255) DEFAULT NULL COMMENT '联行号',
  \`account_name\` varchar(255) DEFAULT NULL COMMENT '账户名称',
  \`account_code\` varchar(255) DEFAULT NULL COMMENT '账户编码',
  \`taxpayer_identify_number\` varchar(255) DEFAULT NULL COMMENT '纳税人识别号',
  \`identity_document\` varchar(255) DEFAULT NULL COMMENT '身份证号',
  \`bank_full_name\` varchar(128) DEFAULT NULL COMMENT '开户支行全称',
  \`bank_card_number\` varchar(50) DEFAULT NULL COMMENT '银行账号',
  \`reserved_phone\` varchar(64) DEFAULT NULL COMMENT '预留手机号',
  \`account_remark\` varchar(650) DEFAULT NULL COMMENT '账户信息备注',
  \`settlement_type\` int(10) unsigned DEFAULT NULL COMMENT '结算方式:1=按车结算,2=按次结算',
  \`cooperation_mode\` int(11) DEFAULT NULL COMMENT '合作模式(1-自有服务商;2-项目方服务商;3-自有&项目方服务商)',
  \`is_secondary_agency\` char(1) DEFAULT NULL COMMENT '是否存在二级服务商 (2-否;1-是)',
  \`agency_status\` char(1) DEFAULT NULL COMMENT '服务商状态(1-启用;2-禁用)',
  \`service_start_date\` date DEFAULT NULL COMMENT '合约开始日期',
  \`service_end_date\` date DEFAULT NULL COMMENT '合约截止日期',
  \`business_begin_time\` time DEFAULT NULL COMMENT '营业开始时间',
  \`business_end_time\` time DEFAULT NULL COMMENT '营业结束时间',
  \`create_time\` datetime DEFAULT NULL COMMENT '创建时间',
  \`create_by\` varchar(20) DEFAULT NULL COMMENT '创建者',
  \`update_time\` datetime DEFAULT NULL COMMENT '更新时间',
  \`update_by\` varchar(20) DEFAULT NULL COMMENT '更新者',
  \`company_domain\` varchar(50) DEFAULT NULL COMMENT '公司域名',
  \`partner_business_id\` varchar(50) DEFAULT NULL COMMENT '合作通知业务id',
  \`del_flag\` char(1) DEFAULT NULL COMMENT '删除标志（1=存在 2=删除）',
  \`empty_drive\` char(1) DEFAULT NULL COMMENT '是否支持空驶(1=是,2=否)',
  \`remark\` varchar(650) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (\`agency_id\`)
) ENGINE=InnoDB AUTO_INCREMENT=469 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;`;

// 新的问题SQL - 包含 CURRENT_TIMESTAMP
const problemSQL = \`CREATE TABLE \`sys_table_customize_header_config\`  (
  \`id\` bigint(20) NOT NULL AUTO_INCREMENT,
  \`customize_table_name\` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义表头名称',
  \`customize_table_field\` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义表头字段',
  \`customize_table_sign\` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义表头标识',
  \`is_default\` tinyint(2) NULL DEFAULT 0 COMMENT '是否默认：1-是，0-否',
  \`create_time\` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  \`create_by\` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  \`update_time\` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  \`update_by\` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (\`id\`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义表头配置' ROW_FORMAT = Dynamic;\`;

// 测试混合SQL语句（包含长CREATE TABLE和其他语句）
const mixedSQL = `${longCreateTableSQL}

${problemSQL}

INSERT INTO \`orders\` (user_id, amount) VALUES (1, 100);
UPDATE products SET price = 99 WHERE id = 10;
SELECT * FROM \`agency\` WHERE agency_id = 1;
ALTER TABLE \`users\` ADD email VARCHAR(100);`;

console.log('=== 测试SQL语句分割和处理 ===\n');

// 模拟SQLPrefixer类的基本功能进行测试
class TestSQLPrefixer {
  smartSQLSplit(text) {
    const statements = [];
    let currentStatement = '';
    let inSingleQuote = false;
    let inDoubleQuote = false;
    let inBacktick = false;
    let inLineComment = false;
    let inBlockComment = false;
    let parenthesesLevel = 0;

    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const nextChar = text[i + 1];
      const prevChar = text[i - 1];

      // 处理行注释
      if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inBlockComment) {
        if (char === '-' && nextChar === '-') {
          inLineComment = true;
          currentStatement += char;
          continue;
        }
      }

      // 行注释结束
      if (inLineComment && (char === '\n' || char === '\r')) {
        inLineComment = false;
        currentStatement += char;
        continue;
      }

      // 处理块注释
      if (!inSingleQuote && !inDoubleQuote && !inBacktick && !inLineComment) {
        if (char === '/' && nextChar === '*') {
          inBlockComment = true;
          currentStatement += char;
          continue;
        }
      }

      // 块注释结束
      if (inBlockComment && char === '*' && nextChar === '/') {
        inBlockComment = false;
        currentStatement += char + nextChar;
        i++; // 跳过下一个字符
        continue;
      }

      // 如果在注释中，直接添加字符
      if (inLineComment || inBlockComment) {
        currentStatement += char;
        continue;
      }

      // 处理字符串引号
      if (char === "'" && !inDoubleQuote && !inBacktick) {
        if (prevChar !== '\\') {
          inSingleQuote = !inSingleQuote;
        }
      } else if (char === '"' && !inSingleQuote && !inBacktick) {
        if (prevChar !== '\\') {
          inDoubleQuote = !inDoubleQuote;
        }
      } else if (char === '`' && !inSingleQuote && !inDoubleQuote) {
        if (prevChar !== '\\') {
          inBacktick = !inBacktick;
        }
      }

      // 处理括号层级
      if (!inSingleQuote && !inDoubleQuote && !inBacktick) {
        if (char === '(') {
          parenthesesLevel++;
        } else if (char === ')') {
          parenthesesLevel--;
        }
      }

      // 处理分号
      if (char === ';' && !inSingleQuote && !inDoubleQuote && !inBacktick && parenthesesLevel === 0) {
        currentStatement += char;
        const trimmedStatement = currentStatement.trim();
        if (trimmedStatement.length > 0) {
          statements.push(trimmedStatement);
        }
        currentStatement = '';
      } else {
        currentStatement += char;
      }
    }

    // 添加最后一个语句
    const trimmedStatement = currentStatement.trim();
    if (trimmedStatement.length > 0) {
      statements.push(trimmedStatement);
    }

    return statements;
  }

  extractSQLStatements(text) {
    return this.smartSQLSplit(text)
      .map(sql => sql.trim())
      .filter(sql => sql.length > 0);
  }
}

const testPrefixer = new TestSQLPrefixer();

console.log('1. 测试简单分号分割:');
const simpleStatements = mixedSQL.split(';').map(s => s.trim()).filter(s => s.length > 0);
console.log(`简单分割结果: ${simpleStatements.length} 条语句`);
simpleStatements.forEach((stmt, i) => {
  console.log(`  ${i + 1}. ${stmt.substring(0, 50)}${stmt.length > 50 ? '...' : ''}`);
});

console.log('\n2. 测试智能分割:');
const smartStatements = testPrefixer.extractSQLStatements(mixedSQL);
console.log(`智能分割结果: ${smartStatements.length} 条语句`);
smartStatements.forEach((stmt, i) => {
  console.log(`  ${i + 1}. ${stmt.substring(0, 50)}${stmt.length > 50 ? '...' : ''}`);
  console.log(`     长度: ${stmt.length} 字符`);
});

console.log('\n3. 对比分析:');
console.log(`简单分割: ${simpleStatements.length} 条`);
console.log(`智能分割: ${smartStatements.length} 条`);
console.log(`差异: ${simpleStatements.length - smartStatements.length} 条 (正数表示简单分割多分了)`);

if (smartStatements.length < simpleStatements.length) {
  console.log('✅ 智能分割成功减少了错误分割!');
} else {
  console.log('⚠️  需要进一步优化分割逻辑');
}

console.log('\n4. 测试问题SQL (CURRENT_TIMESTAMP):');
console.log('原始SQL:');
console.log(problemSQL.substring(0, 100) + '...');

// 这里我们需要模拟SQLPrefixer的addDatabasePrefix方法
// 由于这是测试文件，我们简化处理
console.log('\n期望结果: CURRENT_TIMESTAMP 不应该被添加数据库前缀');
console.log('实际测试需要在浏览器中运行完整的应用来验证');
