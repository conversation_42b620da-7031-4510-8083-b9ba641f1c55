// 简单测试Properties和YAML转换器的注释功能
const { PropertiesYamlConverter } = require('./src/utils/propertiesYamlConverter.ts');

const converter = new PropertiesYamlConverter();

// 测试Properties到YAML的转换
const propertiesText = `# 应用配置
app.name=My Application
app.version=1.0.0
app.debug=true

# 数据库配置
database.host=localhost
database.port=3306
database.name=mydb`;

console.log('=== Properties 输入 ===');
console.log(propertiesText);

const result = converter.propertiesToYaml(propertiesText);

console.log('\n=== YAML 输出 ===');
if (result.success) {
  console.log(result.result);
} else {
  console.log('转换失败:', result.error);
}

// 测试YAML到Properties的转换
const yamlText = `# 应用配置
app:
  name: My Application
  version: 1.0.0
  debug: true

# 数据库配置
database:
  host: localhost
  port: 3306
  name: mydb`;

console.log('\n\n=== YAML 输入 ===');
console.log(yamlText);

const result2 = converter.yamlToProperties(yamlText);

console.log('\n=== Properties 输出 ===');
if (result2.success) {
  console.log(result2.result);
} else {
  console.log('转换失败:', result2.error);
}
