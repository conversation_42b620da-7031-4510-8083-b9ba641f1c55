// 简单测试反引号功能
import { SQLPrefixer } from './src/utils/sqlPrefixer.js';

const sqlPrefixer = new SQLPrefixer();

// 测试用例
const testCases = [
  {
    name: '普通表名',
    sql: 'INSERT INTO orders (user_id, amount) VALUES (1, 100)',
    expected: 'INSERT INTO testdb.orders (user_id, amount) VALUES (1, 100)'
  },
  {
    name: '反引号表名',
    sql: 'INSERT INTO `orders` (user_id, amount) VALUES (1, 100)',
    expected: 'INSERT INTO `testdb`.`orders` (user_id, amount) VALUES (1, 100)'
  },
  {
    name: '混合表名',
    sql: 'SELECT * FROM users JOIN `orders` ON users.id = `orders`.user_id',
    expected: 'SELECT * FROM testdb.users JOIN `testdb`.`orders` ON testdb.users.id = `testdb`.`orders`.user_id'
  },
  {
    name: 'ALTER TABLE with backticks',
    sql: 'ALTER TABLE `products` MODIFY COLUMN price DECIMAL(10,2)',
    expected: 'ALTER TABLE `testdb`.`products` MODIFY COLUMN price DECIMAL(10,2)'
  }
];

console.log('测试反引号功能...\n');

testCases.forEach((testCase, index) => {
  console.log(`测试 ${index + 1}: ${testCase.name}`);
  console.log(`输入: ${testCase.sql}`);
  
  const result = sqlPrefixer.addDatabasePrefix(testCase.sql, 'testdb');
  console.log(`输出: ${result.modified}`);
  console.log(`期望: ${testCase.expected}`);
  console.log(`匹配: ${result.modified === testCase.expected ? '✅' : '❌'}`);
  console.log(`找到的表: [${result.tablesFound.join(', ')}]`);
  console.log('---');
});
